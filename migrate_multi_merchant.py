#!/usr/bin/env python3
"""
多商户系统数据库迁移脚本

此脚本将现有的单商户系统迁移到多商户架构：
1. 创建商户相关表
2. 创建默认商户
3. 迁移现有数据到默认商户
4. 更新数据库结构

使用方法:
python migrate_multi_merchant.py [--dry-run] [--default-merchant-code=default]
"""

import sys
import os
import argparse
import logging
from datetime import datetime
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text, MetaData, Table, Column, Integer, String, Boolean, TIMESTAMP, JSON, DECIMAL, ForeignKey, Index
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import func
from sqlalchemy.exc import SQLAlchemyError

from app.database import get_database_url
from app.models.merchant import Merchant, MerchantWeChatConfig, MerchantStatus
from config.wechat_pay_config import wechat_pay_settings

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'migration_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class MultiMerchantMigration:
    """多商户迁移类"""
    
    def __init__(self, database_url: str, dry_run: bool = False):
        self.database_url = database_url
        self.dry_run = dry_run
        self.engine = create_engine(database_url)
        self.SessionLocal = sessionmaker(bind=self.engine)
        self.metadata = MetaData()
        
        logger.info(f"初始化迁移工具，数据库: {database_url}")
        logger.info(f"干运行模式: {dry_run}")
    
    def check_prerequisites(self) -> bool:
        """检查迁移前提条件"""
        logger.info("检查迁移前提条件...")
        
        try:
            with self.engine.connect() as conn:
                # 检查是否已经存在商户表
                result = conn.execute(text("""
                    SELECT COUNT(*) as count 
                    FROM information_schema.tables 
                    WHERE table_schema = DATABASE() 
                    AND table_name = 'merchants'
                """))
                
                if result.fetchone()[0] > 0:
                    logger.warning("商户表已存在，可能已经迁移过")
                    return False
                
                # 检查必要的表是否存在
                required_tables = ['users', 'orders', 'payment_records', 'user_memberships', 'membership_plans']
                for table in required_tables:
                    result = conn.execute(text(f"""
                        SELECT COUNT(*) as count 
                        FROM information_schema.tables 
                        WHERE table_schema = DATABASE() 
                        AND table_name = '{table}'
                    """))
                    
                    if result.fetchone()[0] == 0:
                        logger.error(f"必要的表 {table} 不存在")
                        return False
                
                logger.info("前提条件检查通过")
                return True
                
        except Exception as e:
            logger.error(f"检查前提条件失败: {e}")
            return False
    
    def create_merchant_tables(self):
        """创建商户相关表"""
        logger.info("创建商户相关表...")
        
        if self.dry_run:
            logger.info("[DRY RUN] 将创建商户表")
            return
        
        try:
            with self.engine.connect() as conn:
                # 创建商户表
                conn.execute(text("""
                    CREATE TABLE merchants (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        merchant_code VARCHAR(50) NOT NULL UNIQUE,
                        merchant_name VARCHAR(100) NOT NULL,
                        description TEXT,
                        contact_name VARCHAR(50),
                        contact_phone VARCHAR(20),
                        contact_email VARCHAR(100),
                        status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
                        business_config JSON,
                        commission_rate DECIMAL(5,4) DEFAULT 0.0000,
                        api_secret VARCHAR(64),
                        allowed_ips JSON,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        INDEX idx_merchant_code (merchant_code),
                        INDEX idx_status (status)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """))
                
                # 创建商户微信配置表
                conn.execute(text("""
                    CREATE TABLE merchant_wechat_configs (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        merchant_id INT NOT NULL,
                        app_id VARCHAR(50) NOT NULL,
                        app_secret VARCHAR(100) NOT NULL,
                        app_name VARCHAR(100),
                        mch_id VARCHAR(20) NOT NULL,
                        api_key VARCHAR(64) NOT NULL,
                        api_v3_key VARCHAR(64) NOT NULL,
                        cert_path VARCHAR(255),
                        key_path VARCHAR(255),
                        cert_serial_no VARCHAR(64),
                        notify_url VARCHAR(255) NOT NULL,
                        is_sandbox BOOLEAN DEFAULT FALSE,
                        order_expire_minutes INT DEFAULT 30,
                        order_prefix VARCHAR(10) DEFAULT 'RS',
                        payment_timeout_seconds INT DEFAULT 30,
                        max_retry_times INT DEFAULT 3,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
                        INDEX idx_merchant_id (merchant_id),
                        INDEX idx_is_active (is_active)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """))
                
                # 创建商户API日志表
                conn.execute(text("""
                    CREATE TABLE merchant_api_logs (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        merchant_id INT NOT NULL,
                        api_path VARCHAR(255) NOT NULL,
                        http_method VARCHAR(10) NOT NULL,
                        request_ip VARCHAR(45),
                        user_agent TEXT,
                        request_params JSON,
                        request_body TEXT,
                        response_status INT,
                        response_body TEXT,
                        response_time_ms INT,
                        error_message TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (merchant_id) REFERENCES merchants(id) ON DELETE CASCADE,
                        INDEX idx_merchant_id (merchant_id),
                        INDEX idx_created_at (created_at),
                        INDEX idx_api_path (api_path)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """))
                
                conn.commit()
                logger.info("商户表创建成功")
                
        except Exception as e:
            logger.error(f"创建商户表失败: {e}")
            raise
    
    def create_default_merchant(self, merchant_code: str = "default") -> int:
        """创建默认商户"""
        logger.info(f"创建默认商户: {merchant_code}")
        
        if self.dry_run:
            logger.info(f"[DRY RUN] 将创建默认商户: {merchant_code}")
            return 1
        
        try:
            session = self.SessionLocal()
            
            # 创建默认商户
            merchant = Merchant(
                merchant_code=merchant_code,
                merchant_name="默认商户",
                description="系统默认商户，用于迁移现有数据",
                status=MerchantStatus.ACTIVE,
                business_config={}
            )
            
            session.add(merchant)
            session.commit()
            session.refresh(merchant)
            
            merchant_id = merchant.id
            logger.info(f"默认商户创建成功，ID: {merchant_id}")
            
            # 创建默认微信配置
            wechat_config = MerchantWeChatConfig(
                merchant_id=merchant_id,
                app_id=wechat_pay_settings.WECHAT_PAY_APPID or "default_app_id",
                app_secret="default_app_secret",  # 需要后续配置
                mch_id=wechat_pay_settings.WECHAT_PAY_MCHID or "default_mch_id",
                api_key="default_api_key",  # 需要后续配置
                api_v3_key=wechat_pay_settings.WECHAT_PAY_API_V3_KEY or "default_api_v3_key",
                cert_path=wechat_pay_settings.WECHAT_PAY_CERT_PATH,
                key_path=wechat_pay_settings.WECHAT_PAY_KEY_PATH,
                cert_serial_no=wechat_pay_settings.WECHAT_PAY_CERT_SERIAL_NO,
                notify_url=wechat_pay_settings.WECHAT_PAY_NOTIFY_URL,
                is_sandbox=wechat_pay_settings.WECHAT_PAY_SANDBOX,
                order_expire_minutes=wechat_pay_settings.ORDER_EXPIRE_MINUTES,
                order_prefix=wechat_pay_settings.ORDER_PREFIX
            )
            
            session.add(wechat_config)
            session.commit()
            
            session.close()
            
            logger.info("默认微信配置创建成功")
            return merchant_id
            
        except Exception as e:
            logger.error(f"创建默认商户失败: {e}")
            raise

    def add_merchant_columns(self, default_merchant_id: int):
        """为现有表添加merchant_id字段"""
        logger.info("为现有表添加merchant_id字段...")

        tables_to_update = [
            'users', 'orders', 'payment_records',
            'user_memberships', 'membership_plans'
        ]

        if self.dry_run:
            logger.info(f"[DRY RUN] 将为以下表添加merchant_id字段: {tables_to_update}")
            return

        try:
            with self.engine.connect() as conn:
                for table in tables_to_update:
                    # 检查字段是否已存在
                    result = conn.execute(text(f"""
                        SELECT COUNT(*) as count
                        FROM information_schema.columns
                        WHERE table_schema = DATABASE()
                        AND table_name = '{table}'
                        AND column_name = 'merchant_id'
                    """))

                    if result.fetchone()[0] == 0:
                        # 添加merchant_id字段
                        conn.execute(text(f"""
                            ALTER TABLE {table}
                            ADD COLUMN merchant_id INT DEFAULT {default_merchant_id},
                            ADD INDEX idx_merchant_id (merchant_id)
                        """))

                        # 更新所有现有记录的merchant_id
                        conn.execute(text(f"""
                            UPDATE {table}
                            SET merchant_id = {default_merchant_id}
                            WHERE merchant_id IS NULL
                        """))

                        logger.info(f"表 {table} 添加merchant_id字段成功")
                    else:
                        logger.info(f"表 {table} 已存在merchant_id字段")

                conn.commit()

        except Exception as e:
            logger.error(f"添加merchant_id字段失败: {e}")
            raise

    def update_user_constraints(self):
        """更新用户表约束"""
        logger.info("更新用户表约束...")

        if self.dry_run:
            logger.info("[DRY RUN] 将更新用户表约束")
            return

        try:
            with self.engine.connect() as conn:
                # 删除原有的openid唯一约束
                try:
                    conn.execute(text("ALTER TABLE users DROP INDEX openid"))
                    logger.info("删除原有openid唯一约束")
                except:
                    logger.info("原有openid唯一约束不存在")

                # 添加复合唯一约束
                conn.execute(text("""
                    ALTER TABLE users
                    ADD UNIQUE INDEX idx_merchant_openid (merchant_id, openid)
                """))

                conn.commit()
                logger.info("用户表约束更新成功")

        except Exception as e:
            logger.error(f"更新用户表约束失败: {e}")
            raise

    def verify_migration(self, default_merchant_id: int):
        """验证迁移结果"""
        logger.info("验证迁移结果...")

        try:
            with self.engine.connect() as conn:
                # 检查商户表
                result = conn.execute(text("SELECT COUNT(*) FROM merchants"))
                merchant_count = result.fetchone()[0]
                logger.info(f"商户数量: {merchant_count}")

                # 检查各表的merchant_id字段
                tables_to_check = ['users', 'orders', 'payment_records', 'user_memberships', 'membership_plans']

                for table in tables_to_check:
                    result = conn.execute(text(f"""
                        SELECT
                            COUNT(*) as total,
                            COUNT(CASE WHEN merchant_id = {default_merchant_id} THEN 1 END) as with_default_merchant,
                            COUNT(CASE WHEN merchant_id IS NULL THEN 1 END) as null_merchant
                        FROM {table}
                    """))

                    row = result.fetchone()
                    logger.info(f"表 {table}: 总记录数={row[0]}, 默认商户记录数={row[1]}, 空商户记录数={row[2]}")

                    if row[2] > 0:
                        logger.warning(f"表 {table} 存在 {row[2]} 条merchant_id为空的记录")

                logger.info("迁移验证完成")

        except Exception as e:
            logger.error(f"验证迁移结果失败: {e}")
            raise

    def run_migration(self, default_merchant_code: str = "default"):
        """执行完整迁移"""
        logger.info("开始多商户系统迁移...")

        try:
            # 1. 检查前提条件
            if not self.check_prerequisites():
                logger.error("前提条件检查失败，迁移终止")
                return False

            # 2. 创建商户表
            self.create_merchant_tables()

            # 3. 创建默认商户
            default_merchant_id = self.create_default_merchant(default_merchant_code)

            # 4. 为现有表添加merchant_id字段
            self.add_merchant_columns(default_merchant_id)

            # 5. 更新用户表约束
            self.update_user_constraints()

            # 6. 验证迁移结果
            self.verify_migration(default_merchant_id)

            logger.info("多商户系统迁移完成！")
            logger.info(f"默认商户代码: {default_merchant_code}")
            logger.info(f"默认商户ID: {default_merchant_id}")
            logger.info("请记得更新商户的微信配置信息")

            return True

        except Exception as e:
            logger.error(f"迁移失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='多商户系统数据库迁移')
    parser.add_argument('--dry-run', action='store_true', help='干运行模式，不实际执行迁移')
    parser.add_argument('--default-merchant-code', default='default', help='默认商户代码')

    args = parser.parse_args()

    # 获取数据库URL
    database_url = get_database_url()

    # 创建迁移实例
    migration = MultiMerchantMigration(database_url, args.dry_run)

    # 执行迁移
    success = migration.run_migration(args.default_merchant_code)

    if success:
        logger.info("迁移成功完成")
        sys.exit(0)
    else:
        logger.error("迁移失败")
        sys.exit(1)


if __name__ == "__main__":
    main()
