"""
商户管理API路由
"""
from fastapi import APIRouter, HTTPException, Depends, status
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from app.database import get_db
from app.models.merchant import Merchant, MerchantWeChatConfig, MerchantStatus
from app.services.merchant_service import merchant_service
from app.auth.admin_auth import require_admin
from app.schemas.merchant import (
    MerchantCreate, MerchantUpdate, MerchantInfo, MerchantListResponse,
    MerchantWeChatConfigCreate, MerchantWeChatConfigUpdate, MerchantWeChatConfigInfo
)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/merchant",
    tags=["商户管理"],
    responses={404: {"description": "未找到"}},
)


@router.post("/", response_model=MerchantInfo)
async def create_merchant(
    merchant_data: MerchantCreate,
    db: Session = Depends(get_db),
    admin_user = Depends(require_admin)
):
    """
    创建商户
    """
    try:
        merchant = merchant_service.create_merchant(
            db=db,
            merchant_code=merchant_data.merchant_code,
            merchant_name=merchant_data.merchant_name,
            description=merchant_data.description,
            contact_name=merchant_data.contact_name,
            contact_phone=merchant_data.contact_phone,
            contact_email=merchant_data.contact_email,
            business_config=merchant_data.business_config
        )
        
        return MerchantInfo.model_validate(merchant)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.exception("创建商户异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建商户失败"
        )


@router.get("/", response_model=MerchantListResponse)
async def list_merchants(
    include_inactive: bool = False,
    db: Session = Depends(get_db),
    admin_user = Depends(require_admin)
):
    """
    获取商户列表
    """
    try:
        merchants = merchant_service.get_all_merchants(db, include_inactive)
        
        return MerchantListResponse(
            merchants=[MerchantInfo.model_validate(m) for m in merchants],
            total=len(merchants)
        )
        
    except Exception as e:
        logger.exception("获取商户列表异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取商户列表失败"
        )


@router.get("/{merchant_code}", response_model=MerchantInfo)
async def get_merchant(
    merchant_code: str,
    db: Session = Depends(get_db),
    admin_user = Depends(require_admin)
):
    """
    获取商户详情
    """
    try:
        merchant = merchant_service.get_merchant_by_code(db, merchant_code)
        if not merchant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="商户不存在"
            )
        
        return MerchantInfo.model_validate(merchant)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("获取商户详情异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取商户详情失败"
        )


@router.put("/{merchant_code}", response_model=MerchantInfo)
async def update_merchant(
    merchant_code: str,
    merchant_data: MerchantUpdate,
    db: Session = Depends(get_db),
    admin_user = Depends(require_admin)
):
    """
    更新商户信息
    """
    try:
        merchant = merchant_service.get_merchant_by_code(db, merchant_code)
        if not merchant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="商户不存在"
            )
        
        # 更新商户信息
        update_data = merchant_data.model_dump(exclude_unset=True)
        success = merchant_service.update_merchant_config(db, merchant_code, **update_data)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="更新商户信息失败"
            )
        
        # 重新获取更新后的商户信息
        updated_merchant = merchant_service.get_merchant_by_code(db, merchant_code)
        return MerchantInfo.model_validate(updated_merchant)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("更新商户信息异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新商户信息失败"
        )


@router.post("/{merchant_code}/wechat-config", response_model=MerchantWeChatConfigInfo)
async def create_wechat_config(
    merchant_code: str,
    config_data: MerchantWeChatConfigCreate,
    db: Session = Depends(get_db),
    admin_user = Depends(require_admin)
):
    """
    创建商户微信配置
    """
    try:
        merchant = merchant_service.get_merchant_by_code(db, merchant_code)
        if not merchant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="商户不存在"
            )
        
        wechat_config = merchant_service.create_wechat_config(
            db=db,
            merchant_id=merchant.id,
            **config_data.model_dump()
        )
        
        return MerchantWeChatConfigInfo.model_validate(wechat_config)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("创建微信配置异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="创建微信配置失败"
        )


@router.get("/{merchant_code}/wechat-config", response_model=MerchantWeChatConfigInfo)
async def get_wechat_config(
    merchant_code: str,
    db: Session = Depends(get_db),
    admin_user = Depends(require_admin)
):
    """
    获取商户微信配置
    """
    try:
        merchant = merchant_service.get_merchant_by_code(db, merchant_code)
        if not merchant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="商户不存在"
            )
        
        wechat_config = db.query(MerchantWeChatConfig).filter(
            MerchantWeChatConfig.merchant_id == merchant.id,
            MerchantWeChatConfig.is_active == True
        ).first()
        
        if not wechat_config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="微信配置不存在"
            )
        
        return MerchantWeChatConfigInfo.model_validate(wechat_config)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("获取微信配置异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取微信配置失败"
        )


@router.delete("/{merchant_code}/cache")
async def clear_merchant_cache(
    merchant_code: str,
    admin_user = Depends(require_admin)
):
    """
    清除商户配置缓存
    """
    try:
        merchant_service.invalidate_cache(merchant_code)
        
        return {"message": f"商户 {merchant_code} 配置缓存已清除"}
        
    except Exception as e:
        logger.exception("清除缓存异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清除缓存失败"
        )


@router.post("/cache/clear-all")
async def clear_all_cache(
    admin_user = Depends(require_admin)
):
    """
    清除所有商户配置缓存
    """
    try:
        merchant_service.invalidate_cache()
        
        return {"message": "所有商户配置缓存已清除"}
        
    except Exception as e:
        logger.exception("清除所有缓存异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清除缓存失败"
        )
