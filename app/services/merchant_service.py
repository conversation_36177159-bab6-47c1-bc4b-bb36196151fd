"""
商户配置管理服务
"""
import logging
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import and_
import json
from datetime import datetime, timedelta

from app.models.merchant import Merchant, MerchantWeChatConfig, MerchantStatus
from app.database import get_db

logger = logging.getLogger(__name__)


class MerchantConfigCache:
    """商户配置缓存"""
    
    def __init__(self, cache_ttl: int = 300):  # 5分钟缓存
        self.cache_ttl = cache_ttl
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._cache_time: Dict[str, datetime] = {}
    
    def get(self, merchant_code: str) -> Optional[Dict[str, Any]]:
        """获取缓存的商户配置"""
        if merchant_code not in self._cache:
            return None
        
        # 检查缓存是否过期
        cache_time = self._cache_time.get(merchant_code)
        if cache_time and datetime.now() - cache_time > timedelta(seconds=self.cache_ttl):
            self.invalidate(merchant_code)
            return None
        
        return self._cache.get(merchant_code)
    
    def set(self, merchant_code: str, config: Dict[str, Any]):
        """设置商户配置缓存"""
        self._cache[merchant_code] = config
        self._cache_time[merchant_code] = datetime.now()
    
    def invalidate(self, merchant_code: str):
        """清除指定商户的缓存"""
        self._cache.pop(merchant_code, None)
        self._cache_time.pop(merchant_code, None)
    
    def clear_all(self):
        """清除所有缓存"""
        self._cache.clear()
        self._cache_time.clear()


class MerchantService:
    """商户服务"""
    
    def __init__(self):
        self.config_cache = MerchantConfigCache()
    
    def get_merchant_by_code(self, db: Session, merchant_code: str) -> Optional[Merchant]:
        """根据商户代码获取商户信息"""
        return db.query(Merchant).filter(
            and_(
                Merchant.merchant_code == merchant_code,
                Merchant.status == "active"  # 使用字符串而不是枚举
            )
        ).first()
    
    def get_merchant_config(self, db: Session, merchant_code: str) -> Optional[Dict[str, Any]]:
        """获取商户完整配置（包含微信配置）"""
        # 先尝试从缓存获取
        cached_config = self.config_cache.get(merchant_code)
        if cached_config:
            return cached_config
        
        # 从数据库获取
        merchant = self.get_merchant_by_code(db, merchant_code)
        if not merchant:
            logger.warning(f"商户不存在或已停用: {merchant_code}")
            return None
        
        # 获取微信配置
        wechat_config = db.query(MerchantWeChatConfig).filter(
            and_(
                MerchantWeChatConfig.merchant_id == merchant.id,
                MerchantWeChatConfig.is_active == True
            )
        ).first()
        
        if not wechat_config:
            logger.warning(f"商户微信配置不存在: {merchant_code}")
            return None
        
        # 构建配置字典
        config = {
            "merchant": {
                "id": merchant.id,
                "code": merchant.merchant_code,
                "name": merchant.merchant_name,
                "description": merchant.description,
                "status": merchant.status.value,
                "business_config": merchant.business_config or {},
                "commission_rate": float(merchant.commission_rate or 0),
                "api_secret": merchant.api_secret,
                "allowed_ips": merchant.allowed_ips or []
            },
            "wechat": {
                "app_id": wechat_config.app_id,
                "app_secret": wechat_config.app_secret,
                "app_name": wechat_config.app_name,
                "mch_id": wechat_config.mch_id,
                "api_key": wechat_config.api_key,
                "api_v3_key": wechat_config.api_v3_key,
                "cert_path": wechat_config.cert_path,
                "key_path": wechat_config.key_path,
                "cert_serial_no": wechat_config.cert_serial_no,
                "notify_url": wechat_config.notify_url,
                "is_sandbox": wechat_config.is_sandbox,
                "order_expire_minutes": wechat_config.order_expire_minutes,
                "order_prefix": wechat_config.order_prefix,
                "payment_timeout_seconds": wechat_config.payment_timeout_seconds,
                "max_retry_times": wechat_config.max_retry_times
            }
        }
        
        # 缓存配置
        self.config_cache.set(merchant_code, config)
        
        logger.info(f"加载商户配置成功: {merchant_code}")
        return config
    
    def get_wechat_config(self, db: Session, merchant_code: str) -> Optional[Dict[str, Any]]:
        """获取商户微信配置"""
        config = self.get_merchant_config(db, merchant_code)
        return config.get("wechat") if config else None
    
    def create_merchant(
        self, 
        db: Session, 
        merchant_code: str,
        merchant_name: str,
        description: Optional[str] = None,
        contact_name: Optional[str] = None,
        contact_phone: Optional[str] = None,
        contact_email: Optional[str] = None,
        business_config: Optional[Dict[str, Any]] = None
    ) -> Merchant:
        """创建商户"""
        # 检查商户代码是否已存在
        existing = db.query(Merchant).filter(Merchant.merchant_code == merchant_code).first()
        if existing:
            raise ValueError(f"商户代码已存在: {merchant_code}")
        
        merchant = Merchant(
            merchant_code=merchant_code,
            merchant_name=merchant_name,
            description=description,
            contact_name=contact_name,
            contact_phone=contact_phone,
            contact_email=contact_email,
            business_config=business_config or {},
            status=MerchantStatus.ACTIVE
        )
        
        db.add(merchant)
        db.commit()
        db.refresh(merchant)
        
        logger.info(f"创建商户成功: {merchant_code}")
        return merchant
    
    def create_wechat_config(
        self,
        db: Session,
        merchant_id: int,
        app_id: str,
        app_secret: str,
        mch_id: str,
        api_v3_key: str,
        notify_url: str,
        **kwargs
    ) -> MerchantWeChatConfig:
        """创建商户微信配置"""
        # 检查商户是否存在
        merchant = db.query(Merchant).filter(Merchant.id == merchant_id).first()
        if not merchant:
            raise ValueError(f"商户不存在: {merchant_id}")
        
        # 停用现有配置
        db.query(MerchantWeChatConfig).filter(
            MerchantWeChatConfig.merchant_id == merchant_id
        ).update({"is_active": False})
        
        wechat_config = MerchantWeChatConfig(
            merchant_id=merchant_id,
            app_id=app_id,
            app_secret=app_secret,
            mch_id=mch_id,
            api_v3_key=api_v3_key,
            notify_url=notify_url,
            **kwargs
        )
        
        db.add(wechat_config)
        db.commit()
        db.refresh(wechat_config)
        
        # 清除缓存
        self.config_cache.invalidate(merchant.merchant_code)
        
        logger.info(f"创建商户微信配置成功: {merchant.merchant_code}")
        return wechat_config
    
    def update_merchant_config(self, db: Session, merchant_code: str, **kwargs) -> bool:
        """更新商户配置"""
        merchant = self.get_merchant_by_code(db, merchant_code)
        if not merchant:
            return False
        
        for key, value in kwargs.items():
            if hasattr(merchant, key):
                setattr(merchant, key, value)
        
        db.commit()
        
        # 清除缓存
        self.config_cache.invalidate(merchant_code)
        
        logger.info(f"更新商户配置成功: {merchant_code}")
        return True
    
    def invalidate_cache(self, merchant_code: Optional[str] = None):
        """清除缓存"""
        if merchant_code:
            self.config_cache.invalidate(merchant_code)
        else:
            self.config_cache.clear_all()
        
        logger.info(f"清除商户配置缓存: {merchant_code or 'ALL'}")
    
    def get_all_merchants(self, db: Session, include_inactive: bool = False) -> List[Merchant]:
        """获取所有商户"""
        query = db.query(Merchant)
        if not include_inactive:
            query = query.filter(Merchant.status == MerchantStatus.ACTIVE)
        
        return query.all()
    
    def validate_merchant_access(
        self, 
        db: Session, 
        merchant_code: str, 
        request_ip: Optional[str] = None
    ) -> bool:
        """验证商户访问权限"""
        config = self.get_merchant_config(db, merchant_code)
        if not config:
            return False
        
        merchant_info = config["merchant"]
        
        # 检查商户状态
        if merchant_info["status"] != "active":
            logger.warning(f"商户已停用: {merchant_code}")
            return False
        
        # 检查IP白名单
        allowed_ips = merchant_info.get("allowed_ips", [])
        if allowed_ips and request_ip and request_ip not in allowed_ips:
            logger.warning(f"IP不在白名单中: {request_ip}, 商户: {merchant_code}")
            return False
        
        return True


# 创建全局商户服务实例
merchant_service = MerchantService()
