"""
商户相关数据模型
"""
from sqlalchemy import Column, Integer, String, Text, TIMESTAMP, Enum, JSON, Boolean, DECIMAL, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.database import Base
import enum


class MerchantStatus(enum.Enum):
    """商户状态枚举"""
    ACTIVE = "active"        # 活跃
    INACTIVE = "inactive"    # 停用
    SUSPENDED = "suspended"  # 暂停


class Merchant(Base):
    """商户表"""
    __tablename__ = "merchants"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    merchant_code = Column(String(50), unique=True, nullable=False, index=True, comment="商户代码")
    merchant_name = Column(String(100), nullable=False, comment="商户名称")
    description = Column(Text, nullable=True, comment="商户描述")
    
    # 联系信息
    contact_name = Column(String(50), nullable=True, comment="联系人姓名")
    contact_phone = Column(String(20), nullable=True, comment="联系电话")
    contact_email = Column(String(100), nullable=True, comment="联系邮箱")
    
    # 商户状态
    status = Column(Enum(MerchantStatus), default=MerchantStatus.ACTIVE, comment="商户状态")
    
    # 业务配置
    business_config = Column(JSON, nullable=True, comment="业务配置信息")
    
    # 费率配置
    commission_rate = Column(DECIMAL(5, 4), default=0.0000, comment="佣金费率")
    
    # 安全配置
    api_secret = Column(String(64), nullable=True, comment="API密钥")
    allowed_ips = Column(JSON, nullable=True, comment="允许的IP地址列表")
    
    # 时间字段
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )
    
    # 关联关系
    wechat_configs = relationship("MerchantWeChatConfig", back_populates="merchant", cascade="all, delete-orphan")
    users = relationship("User", back_populates="merchant")
    orders = relationship("Order", back_populates="merchant")
    membership_plans = relationship("MembershipPlan", back_populates="merchant")
    payment_records = relationship("PaymentRecord", back_populates="merchant")
    user_memberships = relationship("UserMembership", back_populates="merchant")


class MerchantWeChatConfig(Base):
    """商户微信配置表"""
    __tablename__ = "merchant_wechat_configs"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    merchant_id = Column(Integer, ForeignKey("merchants.id", ondelete="CASCADE"), nullable=False, comment="商户ID")
    
    # 微信小程序配置
    app_id = Column(String(50), nullable=False, comment="微信小程序AppID")
    app_secret = Column(String(100), nullable=False, comment="微信小程序AppSecret")
    app_name = Column(String(100), nullable=True, comment="小程序名称")
    
    # 微信支付配置
    mch_id = Column(String(20), nullable=False, comment="微信支付商户号")
    api_key = Column(String(64), nullable=False, comment="微信支付API密钥")
    api_v3_key = Column(String(64), nullable=False, comment="微信支付APIv3密钥")
    
    # 证书配置
    cert_path = Column(String(255), nullable=True, comment="API证书路径")
    key_path = Column(String(255), nullable=True, comment="API证书私钥路径")
    cert_serial_no = Column(String(64), nullable=True, comment="证书序列号")
    
    # 回调配置
    notify_url = Column(String(255), nullable=False, comment="支付回调URL")
    
    # 环境配置
    is_sandbox = Column(Boolean, default=False, nullable=False, comment="是否沙箱环境")
    
    # 订单配置
    order_expire_minutes = Column(Integer, default=30, nullable=False, comment="订单过期时间(分钟)")
    order_prefix = Column(String(10), default="RS", nullable=False, comment="订单号前缀")
    
    # 支付配置
    payment_timeout_seconds = Column(Integer, default=30, nullable=False, comment="支付请求超时时间(秒)")
    max_retry_times = Column(Integer, default=3, nullable=False, comment="支付请求最大重试次数")
    
    # 状态
    is_active = Column(Boolean, default=True, nullable=False, comment="是否启用")
    
    # 时间字段
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")
    updated_at = Column(
        TIMESTAMP,
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )
    
    # 关联关系
    merchant = relationship("Merchant", back_populates="wechat_configs")


class MerchantApiLog(Base):
    """商户API调用日志表"""
    __tablename__ = "merchant_api_logs"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    merchant_id = Column(Integer, ForeignKey("merchants.id", ondelete="CASCADE"), nullable=False, comment="商户ID")
    
    # 请求信息
    api_path = Column(String(255), nullable=False, comment="API路径")
    http_method = Column(String(10), nullable=False, comment="HTTP方法")
    request_ip = Column(String(45), nullable=True, comment="请求IP")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    
    # 请求参数
    request_params = Column(JSON, nullable=True, comment="请求参数")
    request_body = Column(Text, nullable=True, comment="请求体")
    
    # 响应信息
    response_status = Column(Integer, nullable=True, comment="响应状态码")
    response_body = Column(Text, nullable=True, comment="响应体")
    response_time_ms = Column(Integer, nullable=True, comment="响应时间(毫秒)")
    
    # 错误信息
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 时间字段
    created_at = Column(TIMESTAMP, server_default=func.current_timestamp(), comment="创建时间")


# 为了向后兼容，添加关联关系到现有模型
def add_merchant_relationships():
    """添加商户关联关系到现有模型"""
    from app.models.user import User
    from app.models.payment import Order, PaymentRecord
    
    # 为User模型添加merchant关联
    if not hasattr(User, 'merchant_id'):
        User.merchant_id = Column(Integer, ForeignKey("merchants.id"), nullable=True, comment="商户ID")
        User.merchant = relationship("Merchant", back_populates="users")
    
    # 为Order模型添加merchant关联
    if not hasattr(Order, 'merchant_id'):
        Order.merchant_id = Column(Integer, ForeignKey("merchants.id"), nullable=True, comment="商户ID")
        Order.merchant = relationship("Merchant", back_populates="orders")
    
    # 为PaymentRecord模型添加merchant关联
    if not hasattr(PaymentRecord, 'merchant_id'):
        PaymentRecord.merchant_id = Column(Integer, ForeignKey("merchants.id"), nullable=True, comment="商户ID")
